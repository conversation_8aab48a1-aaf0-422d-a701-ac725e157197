package main

import (
	"crypto/ed25519"
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"net"
	"os"
	"runtime"
	"strings"
	"time"
)

// OSType 操作系统类型
type OSType string

const (
	OSWindows OSType = "windows"
	OSLinux   OSType = "linux"
	OSDarwin  OSType = "darwin" // macOS
	OSFreeBSD OSType = "freebsd"
	OSAll     OSType = "all" // 支持所有操作系统
)

// HostInfo 主机信息
type HostInfo struct {
	Fingerprint  string    `json:"fingerprint"`   // 主机指纹
	OSType       OSType    `json:"os_type"`       // 操作系统类型
	OSVersion    string    `json:"os_version"`    // 操作系统版本
	Hostname     string    `json:"hostname"`      // 主机名
	RegisterTime time.Time `json:"register_time"` // 注册时间
}

// LicenseInfo 授权信息结构
type LicenseInfo struct {
	UserName        string     `json:"user_name"`
	Company         string     `json:"company"`
	ProductName     string     `json:"product_name"`
	Version         string     `json:"version"`
	ExpiryDate      time.Time  `json:"expiry_date"`
	Features        []string   `json:"features"`
	MaxHosts        int        `json:"max_hosts"`
	RegisteredHosts []HostInfo `json:"registered_hosts"` // 已注册的主机信息
	IsTrial         bool       `json:"is_trial"`
	TrialStartDate  time.Time  `json:"trial_start_date"`
	TrialDuration   int        `json:"trial_duration"`
	SupportedOS     []OSType   `json:"supported_os"` // 支持的操作系统类型
}

// SignedLicense 签名后的授权信息
type SignedLicense struct {
	License   LicenseInfo `json:"license"`
	Signature []byte      `json:"signature"`
}

// LicenseManager 授权管理器
type LicenseManager struct {
	publicKey  ed25519.PublicKey
	privateKey ed25519.PrivateKey
}

// NewLicenseManager 创建新的授权管理器
func NewLicenseManager() *LicenseManager {
	pub, priv, err := ed25519.GenerateKey(rand.Reader)
	if err != nil {
		log.Fatal("生成密钥失败:", err)
	}

	return &LicenseManager{
		publicKey:  pub,
		privateKey: priv,
	}
}

// GetCurrentHostInfo 获取当前主机信息
func GetCurrentHostInfo() (*HostInfo, error) {
	// 获取主机名
	hostname, err := os.Hostname()
	if err != nil {
		return nil, err
	}

	// 获取MAC地址
	interfaces, err := net.Interfaces()
	if err != nil {
		return nil, err
	}

	var macAddresses []string
	for _, iface := range interfaces {
		if iface.Flags&net.FlagUp != 0 && iface.Flags&net.FlagLoopback == 0 {
			if iface.HardwareAddr != nil {
				macAddresses = append(macAddresses, iface.HardwareAddr.String())
			}
		}
	}

	// 获取操作系统信息
	osType := OSType(runtime.GOOS)
	osVersion := runtime.GOARCH // 简化版本信息，实际可以获取更详细的版本

	// 组合主机信息生成指纹
	hostInfo := fmt.Sprintf("%s-%s-%s-%s", hostname, osType, osVersion, strings.Join(macAddresses, ","))
	hash := sha256.Sum256([]byte(hostInfo))
	fingerprint := hex.EncodeToString(hash[:])

	return &HostInfo{
		Fingerprint:  fingerprint,
		OSType:       osType,
		OSVersion:    osVersion,
		Hostname:     hostname,
		RegisterTime: time.Now(),
	}, nil
}

// GetHostFingerprint 获取主机指纹（保持向后兼容）
func GetHostFingerprint() (string, error) {
	hostInfo, err := GetCurrentHostInfo()
	if err != nil {
		return "", err
	}
	return hostInfo.Fingerprint, nil
}

// CreateTrialLicense 创建试用授权
func CreateTrialLicense(userName, company, productName, version string, maxHosts int, features []string, supportedOS []OSType) LicenseInfo {
	now := time.Now()
	return LicenseInfo{
		UserName:        userName,
		Company:         company,
		ProductName:     productName,
		Version:         version,
		ExpiryDate:      now.AddDate(0, 0, TrialDurationDays),
		Features:        features,
		MaxHosts:        maxHosts,
		RegisteredHosts: []HostInfo{},
		IsTrial:         true,
		TrialStartDate:  now,
		TrialDuration:   TrialDurationDays,
		SupportedOS:     supportedOS,
	}
}

// CreateFullLicense 创建正式授权
func CreateFullLicense(userName, company, productName, version string, maxHosts int, features []string, validYears int, supportedOS []OSType) LicenseInfo {
	now := time.Now()
	return LicenseInfo{
		UserName:        userName,
		Company:         company,
		ProductName:     productName,
		Version:         version,
		ExpiryDate:      now.AddDate(validYears, 0, 0),
		Features:        features,
		MaxHosts:        maxHosts,
		RegisteredHosts: []HostInfo{},
		IsTrial:         false,
		TrialStartDate:  time.Time{},
		TrialDuration:   0,
		SupportedOS:     supportedOS,
	}
}

// CheckOSSupport 检查当前操作系统是否被支持
func (lm *LicenseManager) CheckOSSupport(signedLicense *SignedLicense, currentOS OSType) error {
	// 如果支持所有操作系统
	for _, supportedOS := range signedLicense.License.SupportedOS {
		if supportedOS == OSAll || supportedOS == currentOS {
			return nil
		}
	}

	return fmt.Errorf("当前操作系统 %s 不在授权支持范围内，支持的系统: %v", currentOS, signedLicense.License.SupportedOS)
}

// VerifyLicense 验证授权信息
func (lm *LicenseManager) VerifyLicense(signedLicense *SignedLicense) error {
	// 序列化授权信息
	licenseData, err := json.Marshal(signedLicense.License)
	if err != nil {
		return fmt.Errorf("序列化授权信息失败: %v", err)
	}

	// 验证签名
	if !ed25519.Verify(lm.publicKey, licenseData, signedLicense.Signature) {
		return fmt.Errorf("授权信息签名验证失败")
	}

	// 检查是否过期
	if time.Now().After(signedLicense.License.ExpiryDate) {
		if signedLicense.License.IsTrial {
			return fmt.Errorf("试用期已过期")
		}
		return fmt.Errorf("授权已过期")
	}

	// 如果是试用版，额外检查试用期限制
	if signedLicense.License.IsTrial {
		trialEndDate := signedLicense.License.TrialStartDate.AddDate(0, 0, signedLicense.License.TrialDuration)
		if time.Now().After(trialEndDate) {
			return fmt.Errorf("试用期已结束")
		}
	}

	return nil
}

// CheckHostLicense 检查当前主机是否有授权
func (lm *LicenseManager) CheckHostLicense(signedLicense *SignedLicense) error {
	// 先验证基本授权信息
	if err := lm.VerifyLicense(signedLicense); err != nil {
		return err
	}

	// 获取当前主机信息
	currentHostInfo, err := GetCurrentHostInfo()
	if err != nil {
		return fmt.Errorf("获取主机信息失败: %v", err)
	}

	// 检查操作系统支持
	if err := lm.CheckOSSupport(signedLicense, currentHostInfo.OSType); err != nil {
		return err
	}

	// 检查当前主机是否在已注册列表中
	for _, registeredHost := range signedLicense.License.RegisteredHosts {
		if registeredHost.Fingerprint == currentHostInfo.Fingerprint {
			return nil // 找到匹配的主机
		}
	}

	return fmt.Errorf("当前主机未授权")
}

// RegisterHost 注册新主机
func (lm *LicenseManager) RegisterHost(signedLicense *SignedLicense, hostInfo *HostInfo) (*SignedLicense, error) {
	// 检查操作系统支持
	if err := lm.CheckOSSupport(signedLicense, hostInfo.OSType); err != nil {
		return nil, err
	}

	// 检查是否超过最大主机数量
	if len(signedLicense.License.RegisteredHosts) >= signedLicense.License.MaxHosts {
		return nil, fmt.Errorf("已达到最大主机数量限制 (%d)", signedLicense.License.MaxHosts)
	}

	// 检查主机是否已注册
	for _, registeredHost := range signedLicense.License.RegisteredHosts {
		if registeredHost.Fingerprint == hostInfo.Fingerprint {
			return signedLicense, nil // 主机已注册
		}
	}

	// 添加新主机
	newLicense := signedLicense.License
	hostInfo.RegisterTime = time.Now()
	newLicense.RegisteredHosts = append(newLicense.RegisteredHosts, *hostInfo)

	// 重新签名
	return lm.GenerateLicense(newLicense)
}

// GenerateLicense 生成签名授权
func (lm *LicenseManager) GenerateLicense(license LicenseInfo) (*SignedLicense, error) {
	// 序列化授权信息
	licenseData, err := json.Marshal(license)
	if err != nil {
		return nil, fmt.Errorf("序列化授权信息失败: %v", err)
	}

	// 使用私钥签名
	signature := ed25519.Sign(lm.privateKey, licenseData)

	return &SignedLicense{
		License:   license,
		Signature: signature,
	}, nil
}

// SaveLicense 保存授权到文件
func (lm *LicenseManager) SaveLicense(signedLicense *SignedLicense, filename string) error {
	data, err := json.MarshalIndent(signedLicense, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化授权文件失败: %v", err)
	}

	err = os.WriteFile(filename, data, 0644)
	if err != nil {
		return fmt.Errorf("保存授权文件失败: %v", err)
	}

	fmt.Printf("授权文件已保存: %s\n", filename)
	return nil
}

// LoadLicense 从文件加载授权
func (lm *LicenseManager) LoadLicense(filename string) (*SignedLicense, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("读取授权文件失败: %v", err)
	}

	var signedLicense SignedLicense
	err = json.Unmarshal(data, &signedLicense)
	if err != nil {
		return nil, fmt.Errorf("解析授权文件失败: %v", err)
	}

	return &signedLicense, nil
}

// GetLicenseStatus 获取授权状态信息
func (lm *LicenseManager) GetLicenseStatus(signedLicense *SignedLicense) map[string]interface{} {
	status := make(map[string]interface{})

	license := signedLicense.License
	now := time.Now()

	status["user_name"] = license.UserName
	status["company"] = license.Company
	status["product"] = fmt.Sprintf("%s %s", license.ProductName, license.Version)
	status["is_trial"] = license.IsTrial
	status["features"] = license.Features
	status["max_hosts"] = license.MaxHosts
	status["registered_hosts_count"] = len(license.RegisteredHosts)
	status["supported_os"] = license.SupportedOS

	// 注册主机详情
	var hostDetails []map[string]interface{}
	for _, host := range license.RegisteredHosts {
		hostDetail := map[string]interface{}{
			"hostname":      host.Hostname,
			"os_type":       host.OSType,
			"os_version":    host.OSVersion,
			"register_time": host.RegisterTime.Format("2006-01-02 15:04:05"),
			"fingerprint":   host.Fingerprint[:16] + "...",
		}
		hostDetails = append(hostDetails, hostDetail)
	}
	status["registered_hosts"] = hostDetails

	if license.IsTrial {
		status["license_type"] = "试用版"
		trialEndDate := license.TrialStartDate.AddDate(0, 0, license.TrialDuration)
		remainingDays := int(time.Until(trialEndDate).Hours() / 24)
		if remainingDays < 0 {
			remainingDays = 0
		}
		status["trial_remaining_days"] = remainingDays
		status["trial_end_date"] = trialEndDate.Format("2006-01-02")
		status["trial_expired"] = now.After(trialEndDate)
	} else {
		status["license_type"] = "正式版"
		remainingDays := int(time.Until(license.ExpiryDate).Hours() / 24)
		if remainingDays < 0 {
			remainingDays = 0
		}
		status["remaining_days"] = remainingDays
		status["expiry_date"] = license.ExpiryDate.Format("2006-01-02")
		status["expired"] = now.After(license.ExpiryDate)
	}

	return status
}

// TrialDurationDays 试用期天数
const TrialDurationDays = 30

// printUsage 打印使用说明
func printUsage() {
	fmt.Println("授权管理工具")
	fmt.Println("用法:")
	fmt.Println("  生成授权: ./auth -mode=generate [选项]")
	fmt.Println("  验证授权: ./auth -mode=verify -file=<授权文件>")
	fmt.Println("  查看主机信息: ./auth -mode=info")
	fmt.Println("")
	fmt.Println("生成授权选项:")
	fmt.Println("  -user=<用户名>        用户名 (默认: 测试用户)")
	fmt.Println("  -company=<公司名>     公司名 (默认: 测试公司)")
	fmt.Println("  -product=<产品名>     产品名 (默认: 测试产品)")
	fmt.Println("  -version=<版本>       版本 (默认: v1.0.0)")
	fmt.Println("  -trial=<true/false>   是否为试用版 (默认: true)")
	fmt.Println("  -years=<年数>         正式版有效年数 (默认: 1)")
	fmt.Println("  -hosts=<数量>         最大主机数 (默认: 1)")
	fmt.Println("  -features=<功能列表>  功能列表，逗号分隔 (默认: 基础功能)")
	fmt.Println("  -os=<系统列表>        支持的操作系统，逗号分隔 (默认: all)")
	fmt.Println("                        可选: windows,linux,darwin,freebsd,all")
	fmt.Println("  -output=<文件名>      输出文件名 (默认: license.json)")
	fmt.Println("  -register             自动注册当前主机")
	fmt.Println("")
	fmt.Println("示例:")
	fmt.Println("  ./auth -mode=generate -user=张三 -company=ABC公司 -trial=true -output=trial.json -register")
	fmt.Println("  ./auth -mode=generate -user=李四 -trial=false -years=2 -hosts=10 -os=linux,windows")
	fmt.Println("  ./auth -mode=verify -file=license.json")
	fmt.Println("  ./auth -mode=info")
}

// parseOSTypes 解析操作系统类型列表
func parseOSTypes(osStr string) []OSType {
	if osStr == "" || osStr == "all" {
		return []OSType{OSAll}
	}

	parts := strings.Split(osStr, ",")
	var osTypes []OSType
	for _, part := range parts {
		part = strings.TrimSpace(part)
		switch part {
		case "windows":
			osTypes = append(osTypes, OSWindows)
		case "linux":
			osTypes = append(osTypes, OSLinux)
		case "darwin", "macos":
			osTypes = append(osTypes, OSDarwin)
		case "freebsd":
			osTypes = append(osTypes, OSFreeBSD)
		case "all":
			return []OSType{OSAll}
		default:
			fmt.Printf("警告: 未知的操作系统类型 '%s'，已忽略\n", part)
		}
	}
	if len(osTypes) == 0 {
		return []OSType{OSAll}
	}
	return osTypes
}

// parseFeatures 解析功能列表
func parseFeatures(featuresStr string) []string {
	if featuresStr == "" {
		return []string{"基础功能"}
	}

	parts := strings.Split(featuresStr, ",")
	var features []string
	for _, part := range parts {
		part = strings.TrimSpace(part)
		if part != "" {
			features = append(features, part)
		}
	}
	if len(features) == 0 {
		return []string{"基础功能"}
	}
	return features
}

func main() {
	// 定义命令行参数
	mode := flag.String("mode", "", "操作模式: generate(生成授权), verify(验证授权), info(查看主机信息)")
	file := flag.String("file", "", "授权文件路径 (verify模式必需)")

	// 生成授权参数
	user := flag.String("user", "测试用户", "用户名")
	company := flag.String("company", "测试公司", "公司名")
	product := flag.String("product", "测试产品", "产品名")
	version := flag.String("version", "v1.0.0", "版本")
	trial := flag.Bool("trial", true, "是否为试用版")
	years := flag.Int("years", 1, "正式版有效年数")
	hosts := flag.Int("hosts", 1, "最大主机数")
	features := flag.String("features", "基础功能", "功能列表，逗号分隔")
	osTypes := flag.String("os", "all", "支持的操作系统，逗号分隔")
	output := flag.String("output", "license.json", "输出文件名")
	register := flag.Bool("register", false, "自动注册当前主机")

	flag.Parse()

	// 检查模式参数
	if *mode == "" {
		printUsage()
		return
	}

	// 创建授权管理器
	manager := NewLicenseManager()

	switch *mode {
	case "info":
		showHostInfo()

	case "generate":
		generateLicense(manager, *user, *company, *product, *version, *trial, *years, *hosts, *features, *osTypes, *output, *register)

	case "verify":
		if *file == "" {
			fmt.Println("错误: verify模式需要指定 -file 参数")
			printUsage()
			return
		}
		verifyLicense(manager, *file)

	default:
		fmt.Printf("错误: 未知的模式 '%s'\n\n", *mode)
		printUsage()
	}
}

// showHostInfo 显示当前主机信息
func showHostInfo() {
	fmt.Println("=== 当前主机信息 ===")

	currentHostInfo, err := GetCurrentHostInfo()
	if err != nil {
		fmt.Printf("获取主机信息失败: %v\n", err)
		return
	}

	fmt.Printf("主机名: %s\n", currentHostInfo.Hostname)
	fmt.Printf("操作系统: %s\n", currentHostInfo.OSType)
	fmt.Printf("系统架构: %s\n", currentHostInfo.OSVersion)
	fmt.Printf("主机指纹: %s\n", currentHostInfo.Fingerprint)
	fmt.Printf("当前时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))
}

// generateLicense 生成授权文件
func generateLicense(manager *LicenseManager, user, company, product, version string, trial bool, years, hosts int, featuresStr, osTypesStr, output string, register bool) {
	fmt.Println("=== 生成授权文件 ===")

	// 解析参数
	features := parseFeatures(featuresStr)
	osTypes := parseOSTypes(osTypesStr)

	// 创建授权信息
	var license LicenseInfo
	if trial {
		license = CreateTrialLicense(user, company, product, version, hosts, features, osTypes)
		fmt.Printf("创建试用授权 (有效期: %d 天)\n", TrialDurationDays)
	} else {
		license = CreateFullLicense(user, company, product, version, hosts, features, years, osTypes)
		fmt.Printf("创建正式授权 (有效期: %d 年)\n", years)
	}

	// 如果需要注册当前主机
	if register {
		currentHostInfo, err := GetCurrentHostInfo()
		if err != nil {
			fmt.Printf("获取主机信息失败: %v\n", err)
			return
		}

		// 检查操作系统支持
		supported := false
		for _, supportedOS := range osTypes {
			if supportedOS == OSAll || supportedOS == currentHostInfo.OSType {
				supported = true
				break
			}
		}

		if !supported {
			fmt.Printf("警告: 当前操作系统 %s 不在授权支持范围内 %v\n", currentHostInfo.OSType, osTypes)
			fmt.Println("继续生成授权文件，但当前主机将无法使用...")
		} else {
			license.RegisteredHosts = []HostInfo{*currentHostInfo}
			fmt.Printf("已注册当前主机: %s (%s)\n", currentHostInfo.Hostname, currentHostInfo.OSType)
		}
	}

	// 生成签名授权
	signedLicense, err := manager.GenerateLicense(license)
	if err != nil {
		fmt.Printf("生成授权失败: %v\n", err)
		return
	}

	// 保存到文件
	err = manager.SaveLicense(signedLicense, output)
	if err != nil {
		fmt.Printf("保存授权文件失败: %v\n", err)
		return
	}

	// 显示授权信息
	status := manager.GetLicenseStatus(signedLicense)
	fmt.Printf("\n授权信息:\n")
	fmt.Printf("  用户: %s\n", status["user_name"])
	fmt.Printf("  公司: %s\n", status["company"])
	fmt.Printf("  产品: %s\n", status["product"])
	fmt.Printf("  类型: %s\n", status["license_type"])
	fmt.Printf("  支持的操作系统: %v\n", status["supported_os"])
	fmt.Printf("  最大主机数: %d\n", status["max_hosts"])
	fmt.Printf("  已注册主机数: %d\n", status["registered_hosts_count"])
	fmt.Printf("  功能: %v\n", status["features"])

	if trial {
		fmt.Printf("  试用剩余天数: %d 天\n", status["trial_remaining_days"])
		fmt.Printf("  试用结束日期: %s\n", status["trial_end_date"])
	} else {
		fmt.Printf("  授权剩余天数: %d 天\n", status["remaining_days"])
		fmt.Printf("  授权到期日期: %s\n", status["expiry_date"])
	}

	fmt.Printf("\n授权文件已生成: %s\n", output)
}

// verifyLicense 验证授权文件
func verifyLicense(manager *LicenseManager, filename string) {
	fmt.Printf("=== 验证授权文件: %s ===\n", filename)

	// 加载授权文件
	signedLicense, err := manager.LoadLicense(filename)
	if err != nil {
		fmt.Printf("加载授权文件失败: %v\n", err)
		return
	}

	// 获取当前主机信息
	currentHostInfo, err := GetCurrentHostInfo()
	if err != nil {
		fmt.Printf("获取主机信息失败: %v\n", err)
		return
	}

	fmt.Printf("当前主机: %s (%s %s)\n", currentHostInfo.Hostname, currentHostInfo.OSType, currentHostInfo.OSVersion)
	fmt.Printf("主机指纹: %s\n\n", currentHostInfo.Fingerprint[:16]+"...")

	// 显示授权信息
	status := manager.GetLicenseStatus(signedLicense)
	fmt.Printf("授权信息:\n")
	fmt.Printf("  用户: %s\n", status["user_name"])
	fmt.Printf("  公司: %s\n", status["company"])
	fmt.Printf("  产品: %s\n", status["product"])
	fmt.Printf("  类型: %s\n", status["license_type"])
	fmt.Printf("  支持的操作系统: %v\n", status["supported_os"])
	fmt.Printf("  最大主机数: %d\n", status["max_hosts"])
	fmt.Printf("  已注册主机数: %d\n", status["registered_hosts_count"])
	fmt.Printf("  功能: %v\n", status["features"])

	if signedLicense.License.IsTrial {
		fmt.Printf("  试用剩余天数: %d 天\n", status["trial_remaining_days"])
		fmt.Printf("  试用结束日期: %s\n", status["trial_end_date"])
		if status["trial_expired"].(bool) {
			fmt.Printf("  ⚠️  试用期已过期\n")
		}
	} else {
		fmt.Printf("  授权剩余天数: %d 天\n", status["remaining_days"])
		fmt.Printf("  授权到期日期: %s\n", status["expiry_date"])
		if status["expired"].(bool) {
			fmt.Printf("  ⚠️  授权已过期\n")
		}
	}

	// 显示已注册主机
	if len(signedLicense.License.RegisteredHosts) > 0 {
		fmt.Printf("\n已注册主机:\n")
		for i, host := range signedLicense.License.RegisteredHosts {
			fmt.Printf("  %d. %s (%s %s) - 注册于 %s\n",
				i+1, host.Hostname, host.OSType, host.OSVersion,
				host.RegisterTime.Format("2006-01-02 15:04:05"))
			if host.Fingerprint == currentHostInfo.Fingerprint {
				fmt.Printf("     ✓ 当前主机\n")
			}
		}
	} else {
		fmt.Printf("\n尚未注册任何主机\n")
	}

	// 验证授权
	fmt.Printf("\n=== 授权验证结果 ===\n")

	// 基本授权验证
	if err := manager.VerifyLicense(signedLicense); err != nil {
		fmt.Printf("❌ 基本授权验证失败: %v\n", err)
		return
	}
	fmt.Printf("✓ 基本授权验证通过\n")

	// 操作系统支持验证
	if err := manager.CheckOSSupport(signedLicense, currentHostInfo.OSType); err != nil {
		fmt.Printf("❌ 操作系统支持验证失败: %v\n", err)
		return
	}
	fmt.Printf("✓ 操作系统支持验证通过\n")

	// 主机授权验证
	if err := manager.CheckHostLicense(signedLicense); err != nil {
		fmt.Printf("❌ 主机授权验证失败: %v\n", err)

		// 检查是否可以注册新主机
		if len(signedLicense.License.RegisteredHosts) < signedLicense.License.MaxHosts {
			fmt.Printf("\n💡 提示: 当前主机未注册，但授权还有空余位置 (%d/%d)\n",
				len(signedLicense.License.RegisteredHosts), signedLicense.License.MaxHosts)
			fmt.Printf("   可以使用以下命令注册当前主机:\n")
			fmt.Printf("   ./auth -mode=register -file=%s\n", filename)
		} else {
			fmt.Printf("\n❌ 授权已达到最大主机数量限制 (%d/%d)\n",
				len(signedLicense.License.RegisteredHosts), signedLicense.License.MaxHosts)
		}
		return
	}

	fmt.Printf("✓ 主机授权验证通过\n")
	fmt.Printf("\n🎉 授权验证完全通过！当前主机可以正常使用该软件。\n")
}
