package main

import (
    "crypto/ed25519"
    "crypto/rand"
    "crypto/sha256"
    "encoding/hex"
    "encoding/json"
    "fmt"
    "log"
    "net"
    "os"
    "runtime"
    "strings"
    "time"
)

// OSType 操作系统类型
type OSType string

const (
    OSWindows OSType = "windows"
    OSLinux   OSType = "linux"
    OSDarwin  OSType = "darwin"  // macOS
    OSFreeBSD OSType = "freebsd"
    OSAll     OSType = "all"     // 支持所有操作系统
)

// HostInfo 主机信息
type HostInfo struct {
    Fingerprint string `json:"fingerprint"` // 主机指纹
    OSType      OSType `json:"os_type"`     // 操作系统类型
    OSVersion   string `json:"os_version"`  // 操作系统版本
    Hostname    string `json:"hostname"`    // 主机名
    RegisterTime time.Time `json:"register_time"` // 注册时间
}

// LicenseInfo 授权信息结构
type LicenseInfo struct {
    UserName        string     `json:"user_name"`
    Company         string     `json:"company"`
    ProductName     string     `json:"product_name"`
    Version         string     `json:"version"`
    ExpiryDate      time.Time  `json:"expiry_date"`
    Features        []string   `json:"features"`
    MaxHosts        int        `json:"max_hosts"`
    RegisteredHosts []HostInfo `json:"registered_hosts"` // 已注册的主机信息
    IsTrial         bool       `json:"is_trial"`
    TrialStartDate  time.Time  `json:"trial_start_date"`
    TrialDuration   int        `json:"trial_duration"`
    SupportedOS     []OSType   `json:"supported_os"`     // 支持的操作系统类型
}

// SignedLicense 签名后的授权信息
type SignedLicense struct {
    License   LicenseInfo `json:"license"`
    Signature []byte      `json:"signature"`
}

// LicenseManager 授权管理器
type LicenseManager struct {
    publicKey  ed25519.PublicKey
    privateKey ed25519.PrivateKey
}

// NewLicenseManager 创建新的授权管理器
func NewLicenseManager() *LicenseManager {
    pub, priv, err := ed25519.GenerateKey(rand.Reader)
    if err != nil {
        log.Fatal("生成密钥失败:", err)
    }
    
    return &LicenseManager{
        publicKey:  pub,
        privateKey: priv,
    }
}

// GetCurrentHostInfo 获取当前主机信息
func GetCurrentHostInfo() (*HostInfo, error) {
    // 获取主机名
    hostname, err := os.Hostname()
    if err != nil {
        return nil, err
    }
    
    // 获取MAC地址
    interfaces, err := net.Interfaces()
    if err != nil {
        return nil, err
    }
    
    var macAddresses []string
    for _, iface := range interfaces {
        if iface.Flags&net.FlagUp != 0 && iface.Flags&net.FlagLoopback == 0 {
            if iface.HardwareAddr != nil {
                macAddresses = append(macAddresses, iface.HardwareAddr.String())
            }
        }
    }
    
    // 获取操作系统信息
    osType := OSType(runtime.GOOS)
    osVersion := runtime.GOARCH // 简化版本信息，实际可以获取更详细的版本
    
    // 组合主机信息生成指纹
    hostInfo := fmt.Sprintf("%s-%s-%s-%s", hostname, osType, osVersion, strings.Join(macAddresses, ","))
    hash := sha256.Sum256([]byte(hostInfo))
    fingerprint := hex.EncodeToString(hash[:])
    
    return &HostInfo{
        Fingerprint:  fingerprint,
        OSType:       osType,
        OSVersion:    osVersion,
        Hostname:     hostname,
        RegisterTime: time.Now(),
    }, nil
}

// GetHostFingerprint 获取主机指纹（保持向后兼容）
func GetHostFingerprint() (string, error) {
    hostInfo, err := GetCurrentHostInfo()
    if err != nil {
        return "", err
    }
    return hostInfo.Fingerprint, nil
}

// CreateTrialLicense 创建试用授权
func CreateTrialLicense(userName, company, productName, version string, maxHosts int, features []string, supportedOS []OSType) LicenseInfo {
    now := time.Now()
    return LicenseInfo{
        UserName:        userName,
        Company:         company,
        ProductName:     productName,
        Version:         version,
        ExpiryDate:      now.AddDate(0, 0, TrialDurationDays),
        Features:        features,
        MaxHosts:        maxHosts,
        RegisteredHosts: []HostInfo{},
        IsTrial:         true,
        TrialStartDate:  now,
        TrialDuration:   TrialDurationDays,
        SupportedOS:     supportedOS,
    }
}

// CreateFullLicense 创建正式授权
func CreateFullLicense(userName, company, productName, version string, maxHosts int, features []string, validYears int, supportedOS []OSType) LicenseInfo {
    now := time.Now()
    return LicenseInfo{
        UserName:        userName,
        Company:         company,
        ProductName:     productName,
        Version:         version,
        ExpiryDate:      now.AddDate(validYears, 0, 0),
        Features:        features,
        MaxHosts:        maxHosts,
        RegisteredHosts: []HostInfo{},
        IsTrial:         false,
        TrialStartDate:  time.Time{},
        TrialDuration:   0,
        SupportedOS:     supportedOS,
    }
}

// CheckOSSupport 检查当前操作系统是否被支持
func (lm *LicenseManager) CheckOSSupport(signedLicense *SignedLicense, currentOS OSType) error {
    // 如果支持所有操作系统
    for _, supportedOS := range signedLicense.License.SupportedOS {
        if supportedOS == OSAll || supportedOS == currentOS {
            return nil
        }
    }
    
    return fmt.Errorf("当前操作系统 %s 不在授权支持范围内，支持的系统: %v", currentOS, signedLicense.License.SupportedOS)
}

// VerifyLicense 验证授权信息
func (lm *LicenseManager) VerifyLicense(signedLicense *SignedLicense) error {
    // 序列化授权信息
    licenseData, err := json.Marshal(signedLicense.License)
    if err != nil {
        return fmt.Errorf("序列化授权信息失败: %v", err)
    }
    
    // 验证签名
    if !ed25519.Verify(lm.publicKey, licenseData, signedLicense.Signature) {
        return fmt.Errorf("授权信息签名验证失败")
    }
    
    // 检查是否过期
    if time.Now().After(signedLicense.License.ExpiryDate) {
        if signedLicense.License.IsTrial {
            return fmt.Errorf("试用期已过期")
        }
        return fmt.Errorf("授权已过期")
    }
    
    // 如果是试用版，额外检查试用期限制
    if signedLicense.License.IsTrial {
        trialEndDate := signedLicense.License.TrialStartDate.AddDate(0, 0, signedLicense.License.TrialDuration)
        if time.Now().After(trialEndDate) {
            return fmt.Errorf("试用期已结束")
        }
    }
    
    return nil
}

// CheckHostLicense 检查当前主机是否有授权
func (lm *LicenseManager) CheckHostLicense(signedLicense *SignedLicense) error {
    // 先验证基本授权信息
    if err := lm.VerifyLicense(signedLicense); err != nil {
        return err
    }
    
    // 获取当前主机信息
    currentHostInfo, err := GetCurrentHostInfo()
    if err != nil {
        return fmt.Errorf("获取主机信息失败: %v", err)
    }
    
    // 检查操作系统支持
    if err := lm.CheckOSSupport(signedLicense, currentHostInfo.OSType); err != nil {
        return err
    }
    
    // 检查当前主机是否在已注册列表中
    for _, registeredHost := range signedLicense.License.RegisteredHosts {
        if registeredHost.Fingerprint == currentHostInfo.Fingerprint {
            return nil // 找到匹配的主机
        }
    }
    
    return fmt.Errorf("当前主机未授权")
}

// RegisterHost 注册新主机
func (lm *LicenseManager) RegisterHost(signedLicense *SignedLicense, hostInfo *HostInfo) (*SignedLicense, error) {
    // 检查操作系统支持
    if err := lm.CheckOSSupport(signedLicense, hostInfo.OSType); err != nil {
        return nil, err
    }
    
    // 检查是否超过最大主机数量
    if len(signedLicense.License.RegisteredHosts) >= signedLicense.License.MaxHosts {
        return nil, fmt.Errorf("已达到最大主机数量限制 (%d)", signedLicense.License.MaxHosts)
    }
    
    // 检查主机是否已注册
    for _, registeredHost := range signedLicense.License.RegisteredHosts {
        if registeredHost.Fingerprint == hostInfo.Fingerprint {
            return signedLicense, nil // 主机已注册
        }
    }
    
    // 添加新主机
    newLicense := signedLicense.License
    hostInfo.RegisterTime = time.Now()
    newLicense.RegisteredHosts = append(newLicense.RegisteredHosts, *hostInfo)
    
    // 重新签名
    return lm.GenerateLicense(newLicense)
}

// GetLicenseStatus 获取授权状态信息
func (lm *LicenseManager) GetLicenseStatus(signedLicense *SignedLicense) map[string]interface{} {
    status := make(map[string]interface{})
    
    license := signedLicense.License
    now := time.Now()
    
    status["user_name"] = license.UserName
    status["company"] = license.Company
    status["product"] = fmt.Sprintf("%s %s", license.ProductName, license.Version)
    status["is_trial"] = license.IsTrial
    status["features"] = license.Features
    status["max_hosts"] = license.MaxHosts
    status["registered_hosts_count"] = len(license.RegisteredHosts)
    status["supported_os"] = license.SupportedOS
    
    // 注册主机详情
    var hostDetails []map[string]interface{}
    for _, host := range license.RegisteredHosts {
        hostDetail := map[string]interface{}{
            "hostname":      host.Hostname,
            "os_type":       host.OSType,
            "os_version":    host.OSVersion,
            "register_time": host.RegisterTime.Format("2006-01-02 15:04:05"),
            "fingerprint":   host.Fingerprint[:16] + "...",
        }
        hostDetails = append(hostDetails, hostDetail)
    }
    status["registered_hosts"] = hostDetails
    
    if license.IsTrial {
        status["license_type"] = "试用版"
        trialEndDate := license.TrialStartDate.AddDate(0, 0, license.TrialDuration)
        remainingDays := int(time.Until(trialEndDate).Hours() / 24)
        if remainingDays < 0 {
            remainingDays = 0
        }
        status["trial_remaining_days"] = remainingDays
        status["trial_end_date"] = trialEndDate.Format("2006-01-02")
        status["trial_expired"] = now.After(trialEndDate)
    } else {
        status["license_type"] = "正式版"
        remainingDays := int(time.Until(license.ExpiryDate).Hours() / 24)
        if remainingDays < 0 {
            remainingDays = 0
        }
        status["remaining_days"] = remainingDays
        status["expiry_date"] = license.ExpiryDate.Format("2006-01-02")
        status["expired"] = now.After(license.ExpiryDate)
    }
    
    return status
}

// TrialDurationDays 试用期天数
const TrialDurationDays = 30

func main() {
    // 创建授权管理器
    manager := NewLicenseManager()
    
    // 获取当前主机信息
    currentHostInfo, err := GetCurrentHostInfo()
    if err != nil {
        log.Fatal("获取主机信息失败:", err)
    }
    
    fmt.Printf("当前主机信息:\n")
    fmt.Printf("  主机名: %s\n", currentHostInfo.Hostname)
    fmt.Printf("  操作系统: %s\n", currentHostInfo.OSType)
    fmt.Printf("  系统架构: %s\n", currentHostInfo.OSVersion)
    fmt.Printf("  主机指纹: %s\n\n", currentHostInfo.Fingerprint[:16]+"...")
    
    // 创建支持多操作系统的试用授权
    fmt.Println("=== 创建跨平台试用授权 ===")
    trialLicense := CreateTrialLicense(
        "张三",
        "ABC科技有限公司",
        "超级软件",
        "v1.0.0",
        2,
        []string{"基础功能"},
        []OSType{OSWindows, OSLinux, OSDarwin}, // 支持Windows、Linux、macOS
    )
    
    // 预注册当前主机
    trialLicense.RegisteredHosts = []HostInfo{*currentHostInfo}
    
    // 生成签名试用授权
    signedTrialLicense, err := manager.GenerateLicense(trialLicense)
    if err != nil {
        log.Fatal("生成试用授权失败:", err)
    }
    
    // 显示试用授权状态
    trialStatus := manager.GetLicenseStatus(signedTrialLicense)
    fmt.Printf("授权类型: %s\n", trialStatus["license_type"])
    fmt.Printf("用户: %s\n", trialStatus["user_name"])
    fmt.Printf("支持的操作系统: %v\n", trialStatus["supported_os"])
    fmt.Printf("试用剩余天数: %d 天\n", trialStatus["trial_remaining_days"])
    fmt.Printf("已注册主机:\n")
    for _, host := range trialStatus["registered_hosts"].([]map[string]interface{}) {
        fmt.Printf("  - %s (%s %s) 注册于 %s\n", 
            host["hostname"], host["os_type"], host["os_version"], host["register_time"])
    }
    
    // 创建仅支持Linux的正式授权
    fmt.Println("\n=== 创建Linux专用正式授权 ===")
    fullLicense := CreateFullLicense(
        "李四",
        "XYZ服务器公司",
        "服务器管理软件",
        "v2.0.0",
        10,
        []string{"基础功能", "高级功能", "集群管理"},
        2,
        []OSType{OSLinux}, // 仅支持Linux
    )
    
    // 生成签名正式授权
    signedFullLicense, err := manager.GenerateLicense(fullLicense)
    if err != nil {
        log.Fatal("生成正式授权失败:", err)
    }
    
    // 显示正式授权状态
    fullStatus := manager.GetLicenseStatus(signedFullLicense)
    fmt.Printf("授权类型: %s\n", fullStatus["license_type"])
    fmt.Printf("用户: %s\n", fullStatus["user_name"])
    fmt.Printf("支持的操作系统: %v\n", fullStatus["supported_os"])
    fmt.Printf("授权剩余天数: %d 天\n", fullStatus["remaining_days"])
    
    // 验证当前主机在跨平台授权下的使用权限
    fmt.Println("\n=== 验证跨平台授权 ===")
    if err := manager.CheckHostLicense(signedTrialLicense); err != nil {
        fmt.Printf("授权验证失败: %v\n", err)
    } else {
        fmt.Printf("授权验证成功！当前 %s 系统可以使用该软件\n", currentHostInfo.OSType)
    }
    
    // 验证当前主机在Linux专用授权下的使用权限
    fmt.Println("\n=== 验证Linux专用授权 ===")
    if err := manager.CheckHostLicense(signedFullLicense); err != nil {
        fmt.Printf("授权验证失败: %v\n", err)
        
        // 如果当前是Linux系统，尝试注册
        if currentHostInfo.OSType == OSLinux {
            fmt.Println("尝试注册当前Linux主机...")
            newLicense, regErr := manager.RegisterHost(signedFullLicense, currentHostInfo)
            if regErr != nil {
                fmt.Printf("主机注册失败: %v\n", regErr)
            } else {
                fmt.Println("Linux主机注册成功！")
                manager.SaveLicense(newLicense, "linux_license.json")
            }
        }
    } else {
        fmt.Printf("授权验证成功！当前 %s 系统可以使用该软件\n", currentHostInfo.OSType)
    }
    
    // 保存授权文件
    manager.SaveLicense(signedTrialLicense, "cross_platform_trial.json")
    manager.SaveLicense(signedFullLicense, "linux_only_full.json")
    
    fmt.Println("\n授权文件已保存完成")
}